# Mempool Arbitrage Bot

A real-time mempool monitoring arbitrage bot that detects price-impacting transactions and executes flashloan arbitrage opportunities.

## Features

- **Real-time Mempool Monitoring**: WebSocket-based monitoring of pending transactions
- **DEX Price Aggregation**: Real-time price tracking across major DEXes (Uniswap, Sushiswap, Curve)
- **Arbitrage Detection**: Predicts profitable opportunities from mempool analysis
- **Flashloan Execution**: Atomic arbitrage execution using Aave flashloans
- **Flashbots Integration**: Private bundle submission to avoid MEV competition
- **Gas Optimization**: Smart gas pricing and transaction optimization

## How It Works

1. **Mempool Scanner** monitors pending transactions for large trades that will impact DEX prices
2. **Price Aggregator** maintains real-time price feeds from multiple DEXes
3. **Opportunity Detector** analyzes pending transactions to predict arbitrage opportunities
4. **Execution Engine** executes flashloan arbitrage when profitable opportunities are detected
5. **Flashbots Bundle** submits transactions privately to avoid front-running

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Mempool        │    │  DEX Price      │    │  Opportunity    │
│  Monitor        │───▶│  Aggregator     │───▶│  Detector       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐             │
│  Flashbots      │◀───│  Execution      │◀────────────┘
│  Bundle         │    │  Engine         │
└─────────────────┘    └─────────────────┘
```

## Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Compile Contracts**
   ```bash
   npm run compile
   ```

4. **Deploy Contracts**
   ```bash
   npm run deploy
   ```

5. **Start Monitoring**
   ```bash
   npm start
   ```

## Configuration

Key configuration options in `.env`:

- `MIN_PROFIT_USD`: Minimum profit threshold for execution
- `MAX_GAS_PRICE_GWEI`: Maximum gas price for transactions
- `FLASH_LOAN_AMOUNT_ETH`: Default flashloan amount
- `SLIPPAGE_TOLERANCE`: Maximum acceptable slippage

## Safety Features

- **Profit Validation**: All transactions are validated for profitability before execution
- **Gas Price Limits**: Automatic gas price monitoring and limits
- **Slippage Protection**: Built-in slippage tolerance checks
- **Emergency Stop**: Manual emergency stop functionality

## Monitoring

The bot provides real-time monitoring of:
- Mempool transaction analysis
- DEX price movements
- Arbitrage opportunities detected
- Execution results and profits

## Risk Disclaimer

This bot involves financial risk. Always:
- Test thoroughly on testnets first
- Start with small amounts
- Monitor gas costs and market conditions
- Understand the risks of MEV and arbitrage trading

## License

MIT License
