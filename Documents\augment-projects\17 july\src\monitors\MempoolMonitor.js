const WebSocket = require('ws');
const { ethers } = require('ethers');
const EventEmitter = require('events');
const logger = require('../utils/logger');

/**
 * MempoolMonitor - Real-time mempool monitoring for arbitrage opportunities
 * 
 * This class monitors the Ethereum mempool for pending transactions that could
 * create arbitrage opportunities by impacting DEX prices.
 */
class MempoolMonitor extends EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.ws = null;
        this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
        this.wsProvider = new ethers.WebSocketProvider(config.wsUrl);
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        
        // DEX router addresses to monitor
        this.dexRouters = {
            uniswapV2: '******************************************',
            uniswapV3: '******************************************',
            sushiswap: '******************************************',
            curve: '******************************************',
            balancer: '******************************************'
        };
        
        // Token addresses to monitor
        this.monitoredTokens = new Set([
            '******************************************', // WETH
            '******************************************', // USDC
            '******************************************', // USDT
            '******************************************', // DAI
            '******************************************'  // WBTC
        ]);
        
        this.pendingTransactions = new Map();
        this.processedTxs = new Set();
    }

    /**
     * Start monitoring the mempool
     */
    async start() {
        try {
            logger.info('Starting mempool monitor...');
            await this.connectWebSocket();
            await this.subscribeToMempool();
            this.isConnected = true;
            logger.info('Mempool monitor started successfully');
        } catch (error) {
            logger.error('Failed to start mempool monitor:', error);
            throw error;
        }
    }

    /**
     * Connect to WebSocket provider
     */
    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            try {
                this.wsProvider.on('error', (error) => {
                    logger.error('WebSocket error:', error);
                    this.handleReconnect();
                });

                this.wsProvider.on('close', () => {
                    logger.warn('WebSocket connection closed');
                    this.isConnected = false;
                    this.handleReconnect();
                });

                // Test connection
                this.wsProvider.getNetwork().then(() => {
                    logger.info('WebSocket connected successfully');
                    resolve();
                }).catch(reject);

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Subscribe to pending transactions
     */
    async subscribeToMempool() {
        logger.info('Subscribing to mempool...');
        
        this.wsProvider.on('pending', async (txHash) => {
            try {
                if (this.processedTxs.has(txHash)) return;
                this.processedTxs.add(txHash);
                
                // Get transaction details
                const tx = await this.wsProvider.getTransaction(txHash);
                if (!tx) return;
                
                // Analyze transaction for arbitrage potential
                await this.analyzePendingTransaction(tx);
                
            } catch (error) {
                // Silently handle errors for individual transactions
                // to avoid spam in logs
            }
        });
    }

    /**
     * Analyze pending transaction for arbitrage opportunities
     */
    async analyzePendingTransaction(tx) {
        try {
            // Skip if transaction is too small or gas price too low
            if (!this.isSignificantTransaction(tx)) return;
            
            // Check if transaction interacts with monitored DEXes
            const dexInteraction = this.identifyDexInteraction(tx);
            if (!dexInteraction) return;
            
            // Decode transaction data to understand the trade
            const tradeData = await this.decodeTradeData(tx, dexInteraction);
            if (!tradeData) return;
            
            // Check if this trade could create arbitrage opportunities
            if (this.couldCreateArbitrage(tradeData)) {
                logger.info(`Potential arbitrage opportunity detected from tx: ${tx.hash}`);
                
                // Emit event for opportunity detector
                this.emit('potentialArbitrage', {
                    txHash: tx.hash,
                    dex: dexInteraction.dex,
                    tradeData: tradeData,
                    gasPrice: tx.gasPrice,
                    timestamp: Date.now()
                });
            }
            
        } catch (error) {
            logger.debug(`Error analyzing transaction ${tx.hash}:`, error.message);
        }
    }

    /**
     * Check if transaction is significant enough to monitor
     */
    isSignificantTransaction(tx) {
        // Check minimum value threshold (e.g., $10,000 equivalent)
        const minValueWei = ethers.parseEther('5'); // 5 ETH minimum
        
        return (
            tx.value && tx.value >= minValueWei ||
            (tx.gasPrice && tx.gasPrice >= ethers.parseUnits('20', 'gwei'))
        );
    }

    /**
     * Identify if transaction interacts with monitored DEXes
     */
    identifyDexInteraction(tx) {
        if (!tx.to) return null;
        
        const toAddress = tx.to.toLowerCase();
        
        for (const [dexName, routerAddress] of Object.entries(this.dexRouters)) {
            if (toAddress === routerAddress.toLowerCase()) {
                return {
                    dex: dexName,
                    router: routerAddress
                };
            }
        }
        
        return null;
    }

    /**
     * Decode transaction data to understand the trade
     */
    async decodeTradeData(tx, dexInteraction) {
        try {
            if (!tx.data || tx.data === '0x') return null;
            
            // Basic function signature detection
            const functionSig = tx.data.slice(0, 10);
            
            // Common DEX function signatures
            const dexFunctions = {
                '0x38ed1739': 'swapExactTokensForTokens', // Uniswap V2
                '0x8803dbee': 'swapTokensForExactTokens', // Uniswap V2
                '0x414bf389': 'exactInputSingle', // Uniswap V3
                '0xdb3e2198': 'exactOutputSingle', // Uniswap V3
                '0x3593564c': 'execute' // Universal Router
            };
            
            const functionName = dexFunctions[functionSig];
            if (!functionName) return null;
            
            return {
                function: functionName,
                signature: functionSig,
                value: tx.value,
                gasLimit: tx.gasLimit,
                data: tx.data
            };
            
        } catch (error) {
            return null;
        }
    }

    /**
     * Check if trade could create arbitrage opportunities
     */
    couldCreateArbitrage(tradeData) {
        // Large trades are more likely to create price discrepancies
        const isLargeTrade = tradeData.value && tradeData.value >= ethers.parseEther('10');
        
        // High gas limit suggests complex/large transaction
        const isComplexTrade = tradeData.gasLimit && tradeData.gasLimit >= 300000n;
        
        // Specific functions that often create arbitrage opportunities
        const arbitrageProneFunction = [
            'swapExactTokensForTokens',
            'exactInputSingle',
            'execute'
        ].includes(tradeData.function);
        
        return isLargeTrade || isComplexTrade || arbitrageProneFunction;
    }

    /**
     * Handle WebSocket reconnection
     */
    async handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error('Max reconnection attempts reached');
            this.emit('error', new Error('Failed to reconnect to WebSocket'));
            return;
        }
        
        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
        
        logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(async () => {
            try {
                await this.start();
                this.reconnectAttempts = 0;
            } catch (error) {
                logger.error('Reconnection failed:', error);
                this.handleReconnect();
            }
        }, delay);
    }

    /**
     * Stop monitoring
     */
    async stop() {
        logger.info('Stopping mempool monitor...');
        this.isConnected = false;
        
        if (this.wsProvider) {
            await this.wsProvider.destroy();
        }
        
        this.removeAllListeners();
        logger.info('Mempool monitor stopped');
    }

    /**
     * Get monitoring statistics
     */
    getStats() {
        return {
            isConnected: this.isConnected,
            processedTransactions: this.processedTxs.size,
            pendingTransactions: this.pendingTransactions.size,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

module.exports = MempoolMonitor;
