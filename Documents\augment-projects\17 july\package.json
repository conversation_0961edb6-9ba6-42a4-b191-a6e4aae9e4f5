{"name": "mempool-arbitrage-bot", "version": "1.0.0", "description": "Real-time mempool monitoring arbitrage bot with flashloan execution", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "compile": "npx hardhat compile", "deploy": "npx hardhat run scripts/deploy.js", "monitor": "node src/monitor.js"}, "dependencies": {"ethers": "^6.8.0", "ws": "^8.14.2", "axios": "^1.6.0", "dotenv": "^16.3.1", "@flashbots/ethers-provider-bundle": "^1.0.0", "@aave/protocol-v2": "^1.0.1", "@uniswap/v3-sdk": "^3.10.0", "@uniswap/sdk-core": "^4.2.0", "node-cron": "^3.0.3", "winston": "^3.11.0", "redis": "^4.6.10", "big.js": "^6.2.1", "lodash": "^4.17.21"}, "devDependencies": {"hardhat": "^2.19.0", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["arbitrage", "mempool", "flashloan", "defi", "mev", "ethereum"], "author": "Your Name", "license": "MIT"}