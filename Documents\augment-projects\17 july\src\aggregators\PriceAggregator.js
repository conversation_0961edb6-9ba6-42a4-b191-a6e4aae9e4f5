const { ethers } = require('ethers');
const EventEmitter = require('events');
const logger = require('../utils/logger');

/**
 * PriceAggregator - Real-time price aggregation across multiple DEXes
 * 
 * This class maintains real-time price feeds from major DEXes to detect
 * price discrepancies and arbitrage opportunities.
 */
class PriceAggregator extends EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
        
        // Price cache with timestamps
        this.prices = new Map();
        this.lastUpdate = new Map();
        
        // DEX configurations
        this.dexConfigs = {
            uniswapV2: {
                factory: '******************************************',
                router: '******************************************',
                fee: 0.003 // 0.3%
            },
            uniswapV3: {
                factory: '******************************************',
                router: '******************************************',
                quoter: '******************************************'
            },
            sushiswap: {
                factory: '******************************************',
                router: '******************************************',
                fee: 0.003 // 0.3%
            }
        };
        
        // Common token pairs to monitor
        this.tokenPairs = [
            {
                tokenA: '******************************************', // WETH
                tokenB: '******************************************', // USDC
                symbol: 'WETH/USDC'
            },
            {
                tokenA: '******************************************', // WETH
                tokenB: '******************************************', // USDT
                symbol: 'WETH/USDT'
            },
            {
                tokenA: '******************************************', // USDC
                tokenB: '******************************************', // USDT
                symbol: 'USDC/USDT'
            }
        ];
        
        this.updateInterval = null;
        this.isRunning = false;
    }

    /**
     * Start price aggregation
     */
    async start() {
        try {
            logger.info('Starting price aggregator...');
            
            // Initial price fetch
            await this.updateAllPrices();
            
            // Set up periodic updates
            this.updateInterval = setInterval(async () => {
                await this.updateAllPrices();
            }, this.config.priceUpdateInterval || 5000);
            
            this.isRunning = true;
            logger.info('Price aggregator started successfully');
            
        } catch (error) {
            logger.error('Failed to start price aggregator:', error);
            throw error;
        }
    }

    /**
     * Update prices for all pairs across all DEXes
     */
    async updateAllPrices() {
        const updatePromises = [];
        
        for (const pair of this.tokenPairs) {
            for (const dexName of Object.keys(this.dexConfigs)) {
                updatePromises.push(
                    this.updatePairPrice(dexName, pair).catch(error => {
                        logger.debug(`Failed to update ${pair.symbol} on ${dexName}:`, error.message);
                    })
                );
            }
        }
        
        await Promise.allSettled(updatePromises);
        this.detectArbitrageOpportunities();
    }

    /**
     * Update price for a specific pair on a specific DEX
     */
    async updatePairPrice(dexName, pair) {
        try {
            let price;
            
            switch (dexName) {
                case 'uniswapV2':
                case 'sushiswap':
                    price = await this.getUniswapV2Price(dexName, pair);
                    break;
                case 'uniswapV3':
                    price = await this.getUniswapV3Price(pair);
                    break;
                default:
                    return;
            }
            
            if (price) {
                const key = `${dexName}_${pair.symbol}`;
                this.prices.set(key, price);
                this.lastUpdate.set(key, Date.now());
                
                this.emit('priceUpdate', {
                    dex: dexName,
                    pair: pair.symbol,
                    price: price,
                    timestamp: Date.now()
                });
            }
            
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get price from Uniswap V2 style DEX
     */
    async getUniswapV2Price(dexName, pair) {
        const config = this.dexConfigs[dexName];
        
        // Uniswap V2 Pair ABI (minimal)
        const pairAbi = [
            'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
            'function token0() external view returns (address)',
            'function token1() external view returns (address)'
        ];
        
        // Calculate pair address
        const pairAddress = await this.getPairAddress(config.factory, pair.tokenA, pair.tokenB);
        if (!pairAddress) return null;
        
        const pairContract = new ethers.Contract(pairAddress, pairAbi, this.provider);
        
        const [reserves, token0] = await Promise.all([
            pairContract.getReserves(),
            pairContract.token0()
        ]);
        
        const [reserve0, reserve1] = reserves;
        
        // Determine which token is which
        const isToken0A = token0.toLowerCase() === pair.tokenA.toLowerCase();
        const reserveA = isToken0A ? reserve0 : reserve1;
        const reserveB = isToken0A ? reserve1 : reserve0;
        
        // Calculate price (tokenB per tokenA)
        if (reserveA > 0n && reserveB > 0n) {
            return {
                price: Number(reserveB) / Number(reserveA),
                reserveA: reserveA.toString(),
                reserveB: reserveB.toString(),
                liquidity: (Number(reserveA) * Number(reserveB)) ** 0.5
            };
        }
        
        return null;
    }

    /**
     * Get price from Uniswap V3
     */
    async getUniswapV3Price(pair) {
        const quoterAbi = [
            'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)'
        ];
        
        const quoter = new ethers.Contract(
            this.dexConfigs.uniswapV3.quoter,
            quoterAbi,
            this.provider
        );
        
        // Quote 1 token (with proper decimals)
        const amountIn = ethers.parseEther('1');
        const fee = 3000; // 0.3%
        
        try {
            const amountOut = await quoter.quoteExactInputSingle.staticCall(
                pair.tokenA,
                pair.tokenB,
                fee,
                amountIn,
                0
            );
            
            return {
                price: Number(ethers.formatEther(amountOut)),
                amountIn: amountIn.toString(),
                amountOut: amountOut.toString(),
                fee: fee
            };
            
        } catch (error) {
            // Try different fee tiers
            const feeTiers = [500, 3000, 10000];
            for (const feeAmount of feeTiers) {
                try {
                    const amountOut = await quoter.quoteExactInputSingle.staticCall(
                        pair.tokenA,
                        pair.tokenB,
                        feeAmount,
                        amountIn,
                        0
                    );
                    
                    return {
                        price: Number(ethers.formatEther(amountOut)),
                        amountIn: amountIn.toString(),
                        amountOut: amountOut.toString(),
                        fee: feeAmount
                    };
                } catch (e) {
                    continue;
                }
            }
            throw error;
        }
    }

    /**
     * Calculate Uniswap V2 pair address
     */
    async getPairAddress(factory, tokenA, tokenB) {
        const factoryAbi = [
            'function getPair(address tokenA, address tokenB) external view returns (address pair)'
        ];
        
        const factoryContract = new ethers.Contract(factory, factoryAbi, this.provider);
        const pairAddress = await factoryContract.getPair(tokenA, tokenB);
        
        return pairAddress !== ethers.ZeroAddress ? pairAddress : null;
    }

    /**
     * Detect arbitrage opportunities by comparing prices across DEXes
     */
    detectArbitrageOpportunities() {
        for (const pair of this.tokenPairs) {
            const dexPrices = [];
            
            // Collect prices from all DEXes for this pair
            for (const dexName of Object.keys(this.dexConfigs)) {
                const key = `${dexName}_${pair.symbol}`;
                const price = this.prices.get(key);
                const lastUpdate = this.lastUpdate.get(key);
                
                if (price && lastUpdate && (Date.now() - lastUpdate) < 30000) { // 30 second freshness
                    dexPrices.push({
                        dex: dexName,
                        price: price.price,
                        data: price
                    });
                }
            }
            
            if (dexPrices.length >= 2) {
                // Find min and max prices
                const sortedPrices = dexPrices.sort((a, b) => a.price - b.price);
                const minPrice = sortedPrices[0];
                const maxPrice = sortedPrices[sortedPrices.length - 1];
                
                // Calculate potential profit percentage
                const priceDiff = maxPrice.price - minPrice.price;
                const profitPercentage = (priceDiff / minPrice.price) * 100;
                
                // Emit arbitrage opportunity if significant
                if (profitPercentage > 0.1) { // 0.1% minimum
                    this.emit('arbitrageOpportunity', {
                        pair: pair.symbol,
                        buyDex: minPrice.dex,
                        sellDex: maxPrice.dex,
                        buyPrice: minPrice.price,
                        sellPrice: maxPrice.price,
                        profitPercentage: profitPercentage,
                        timestamp: Date.now()
                    });
                }
            }
        }
    }

    /**
     * Get current price for a specific pair on a specific DEX
     */
    getPrice(dexName, pairSymbol) {
        const key = `${dexName}_${pairSymbol}`;
        return this.prices.get(key);
    }

    /**
     * Get all prices for a specific pair
     */
    getAllPricesForPair(pairSymbol) {
        const prices = {};
        
        for (const dexName of Object.keys(this.dexConfigs)) {
            const key = `${dexName}_${pairSymbol}`;
            const price = this.prices.get(key);
            if (price) {
                prices[dexName] = price;
            }
        }
        
        return prices;
    }

    /**
     * Stop price aggregation
     */
    stop() {
        logger.info('Stopping price aggregator...');
        
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        this.isRunning = false;
        this.removeAllListeners();
        
        logger.info('Price aggregator stopped');
    }

    /**
     * Get aggregator statistics
     */
    getStats() {
        return {
            isRunning: this.isRunning,
            totalPrices: this.prices.size,
            monitoredPairs: this.tokenPairs.length,
            monitoredDexes: Object.keys(this.dexConfigs).length
        };
    }
}

module.exports = PriceAggregator;
